# Operations API Summary

The Operations API provides a structured way to define and execute business operations with built-in validation, preparation, and execution pipelines.

## Core Concepts

Operations are modules that implement a processing pipeline with the following steps:
- **conform** - Validates input against the schema and transforms it
- **prepare** - Prepares the conformed parameters for execution
- **validate** - Validates the prepared parameters (custom validation logic)
- **execute** - Executes the operation with validated parameters
- **finalize** - Finalizes the operation result for the public API

## Basic Usage

### Simple Operation with `execute`

```elixir
defmodule CreateUser do
  use MyApp.Operations, type: :command

  schema do
    %{
      required(:name) => string(:filled?),
      required(:email) => string(:filled?)
    }
  end

  @impl true
  def execute(%{params: params}) do
    # Your business logic here
    user = %{id: :rand.uniform(1000), name: params.name, email: params.email}
    {:ok, user}
  end
end

# Usage
{:ok, user} = CreateUser.call(%{name: "<PERSON>", email: "<EMAIL>"})
# => {:ok, %{id: 123, name: "<PERSON>", email: "<EMAIL>"}}

{:error, errors} = CreateUser.call(%{name: "", email: "<EMAIL>"})
# => {:error, [%{path: [:name], text: "must be filled", ...}]}
```

### Using `prepare` for Parameter Transformation

```elixir
defmodule CreateTemplate do
  use MyApp.Operations, type: :command

  schema do
    %{
      required(:name) => string(:filled?),
      required(:template) => boolean()
    }
  end

  @impl true
  def prepare(%{params: %{template: true} = params} = context) do
    # Transform parameters before execution
    updated_params = Map.put(params, :name, params.name <> ".template")
    Map.put(context, :params, updated_params)
  end

  @impl true
  def execute(%{params: params}) do
    {:ok, params}
  end
end

# Usage
{:ok, result} = CreateTemplate.call(%{name: "README.md", template: true})
# => {:ok, %{name: "README.md.template", template: true}}
```

### Using `validate` for Custom Validation

```elixir
defmodule CreateAccount do
  use MyApp.Operations, type: :command

  schema do
    %{
      required(:email) => string(:filled?),
      required(:password) => string(:filled?)
    }
  end

  @impl true
  def validate(%{params: params} = context) do
    # Custom validation logic beyond schema validation
    if String.length(params.password) < 8 do
      {:error, "password must be at least 8 characters"}
    else
      {:ok, context}
    end
  end

  @impl true
  def execute(%{params: params}) do
    # Create account logic
    {:ok, %{email: params.email, created_at: DateTime.utc_now()}}
  end
end

# Usage
{:ok, account} = CreateAccount.call(%{email: "<EMAIL>", password: "secretpassword"})
{:error, error} = CreateAccount.call(%{email: "<EMAIL>", password: "short"})
# => {:error, "password must be at least 8 characters"}
```

## Key Features

- **Schema Validation**: Built-in parameter validation using Drops contracts
- **Pipeline Processing**: Structured execution flow with prepare → validate → execute → finalize
- **Error Handling**: Consistent `{:ok, result}` | `{:error, error}` return values
- **Operation Composition**: Operations can be chained together using the pipeline operator
- **Extensibility**: Support for extensions that can modify the processing pipeline

## Return Values

All operations return standardized tuples:
- `{:ok, result}` on success
- `{:error, error}` on failure (validation errors, business logic errors, etc.)
